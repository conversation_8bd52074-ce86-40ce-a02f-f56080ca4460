# USER QUERY
Why is my context selection taking so long?

# INTELLIGENT CONTEXT ANALYSIS
## Task: debugging
## Focus: Debug performance issues in context selection

## CRITICAL ENTITIES (8 most important)

### 1. parse_context_request (function)
- File: context_request_handler.py


- Criticality: high | Risk: medium
- **Calls**: ["search", "strip", "group", "rstrip", "startswith", "..."] (total: 13)
- **Used by**: ["test_context_request_missing_reason", "test_json_parsing_fix", "aider_context_request_integration", "test_directory_file_format", "test_context_request_format_fix", "..."] (total: 6)
- **Side Effects**: network_io, writes_log

### 2. process_context_requests (method)
- File: aider-main\aider\coders\base_coder.py
- Belongs to Class: `BaseCoder`
- Inherits From: No inheritance detected
- Criticality: high | Risk: high

#### 🔁 Class Context
- Part of `BaseCoder` class

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ["tool_warning", "getcwd", "basename", "exists", "join", "..."] (total: 15)
- **Used by**: ["test_repo_map_compatibility", "test_aider_coder_path_fix", "test_context_request_hang", "test_llm_workflow_understanding", "test_aider_context_request", "..."] (total: 9)
- **Side Effects**: database_io, writes_log, network_io

### 3. process_context_request (method)
- File: context_request_handler.py
- Belongs to Class: `ContextRequestHandler`
- Inherits From: No inheritance detected
- Criticality: high | Risk: high

#### 🔁 Class Context
- Part of `ContextRequestHandler` class

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ["join", "_get_from_cache", "_extract_symbol_content", "_extract_essential_imports", "SymbolInfo", "..."] (total: 9)
- **Used by**: ["test_partial_context_request", "test_query_distinction", "test_surgical_extraction_integration", "test_context_request_with_repo_map", "test_repo_map_extraction", "..."] (total: 10)
- **Side Effects**: database_io, network_io, writes_log

### 4. process_context_requests (method)
- File: aider-main\aider\coders\base_coder_old.py
- Belongs to Class: `BaseCoderOld`
- Inherits From: No inheritance detected
- Criticality: high | Risk: high

#### 🔁 Class Context
- Part of `BaseCoderOld` class

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ["tool_warning", "AiderContextRequestIntegration", "tool_error", "detect_context_request", "tool_output", "..."] (total: 13)
- **Used by**: ["test_repo_map_compatibility", "test_aider_coder_path_fix", "test_context_request_hang", "test_llm_workflow_understanding", "test_aider_context_request", "..."] (total: 9)
- **Side Effects**: network_io, writes_log, modifies_state

### 5. test_all_possible_context_leaks (function)
- File: test_hidden_context_detection.py


- Criticality: high | Risk: medium
- **Calls**: ["Model", "InputOutput", "GitRepo", "create", "fmt_system_prompt", "..."] (total: 8)
- **Used by**: []
- **Side Effects**: network_io, writes_log

### 6. main (function)
- File: test_real_context_request_fix.py


- Criticality: high | Risk: medium
- **Calls**: ["test_real_context_request_fix"] (total: 1)
- **Used by**: ["test_ssl_verification", "test_deprecated", "test_browser", "test_main"] (total: 4)
- **Side Effects**: network_io, writes_log

### 7. process_context_request (function)
- File: aider_context_request_integration.py


- Criticality: medium | Risk: medium
- **Calls**: ["process_context_request", "get", "render_augmented_prompt"] (total: 3)
- **Used by**: ["test_partial_context_request", "test_query_distinction", "test_surgical_extraction_integration", "test_context_request_with_repo_map", "test_repo_map_extraction", "..."] (total: 10)
- **Side Effects**: network_io, writes_log, modifies_state

### 8. detect_context_request (function)
- File: aider_context_request_integration.py


- Criticality: medium | Risk: medium
- **Calls**: ["parse_context_request", "join"] (total: 2)
- **Used by**: ["test_repo_map_compatibility", "test_context_request_hang", "test_context_request", "test_aider_context_request", "context_request_demo", "..."] (total: 9)
- **Side Effects**: network_io, writes_log, modifies_state

## KEY IMPLEMENTATIONS (8 functions)

### 1. parse_context_request
```python
    def parse_context_request(self, request_text: str) -> Optional[ContextRequest]:
        """
        Parse a context request from the LLM response.

        Args:
            request_text: The text containing the context request

        Returns:
            A ContextRequest object or None if the request is invalid
        """
        try:
            # Extract the JSON object from the request text
            pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}\}'
            match = re.search(pattern, request_text, re.DOTALL)
            if not match:
                # Try alternative pattern
                pattern = r'\{CONTEXT_REQUEST:\s*(.*)'
                match = re.search(pattern, request_text, re.DOTALL)
                if not match:
                    return None

            # Get the matched content
    # ... (implementation continues)
```

### 2. process_context_requests
```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
    # ... (implementation continues)
```

### 3. process_context_request
```python
    def process_context_request(self, request: ContextRequest) -> Dict[str, Any]:
        """
        Process a context request, extracting the requested symbols and their dependencies.

        Args:
            request: The context request to process

        Returns:
            A dictionary containing the extracted context
        """
        # Create a cache key for this request
        cache_key = f"context_request:{','.join([s.name for s in request.symbols_of_interest])}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        result = {
            "original_user_query_context": request.original_user_query_context,
            "reason_for_request": request.reason_for_request,
            "extracted_symbols": [],
    # ... (implementation continues)
```

### 4. process_context_request
```python
    def process_context_request(self,
                               context_request: ContextRequest,
                               original_user_query: str,
                               repo_overview: str) -> str:
        """
        Process a context request and generate an augmented prompt.

        Args:
            context_request: The context request to process
            original_user_query: The original user query
            repo_overview: The repository overview

        Returns:
            An augmented prompt with the extracted context
        """
        # Log the inputs
        print("\n\n=== CONTEXT REQUEST PROCESSING ===")
        print(f"Original user query: {original_user_query}")
        print(f"Context request: {context_request}")
    # ... (implementation continues)
```

### 5. detect_context_request
```python
    def detect_context_request(self, llm_response: str) -> Optional[ContextRequest]:
        """
        Detect if the LLM response contains a context request.

        Args:
            llm_response: The LLM response to check

        Returns:
            A ContextRequest object if found, None otherwise
        """
        print("\n=== DETECTING CONTEXT REQUEST ===")
        print(f"LLM response (first 200 chars): {llm_response[:200]}..." if len(llm_response) > 200 else f"LLM response: {llm_response}")

        context_request = self.context_handler.parse_context_request(llm_response)

        if context_request:
            print(f"Context request detected: {context_request}")
            symbols = [s.name for s in context_request.symbols_of_interest]
    # ... (implementation continues)
```

### 6. process_context_requests
```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
    # ... (implementation continues)
```

### 7. test_all_possible_context_leaks
```python
def test_all_possible_context_leaks():
    """Test ALL possible ways repository context could leak to the LLM."""
    print("🔍 Hidden Repository Context Detection")
    print("=" * 80)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        print("✅ Smart Map Request System is available")
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
    # ... (implementation continues)
```

### 8. main
```python
def main():
    """Main test function."""
    success = test_real_context_request_fix()

    if success:
        print("\n🎉 REAL CONTEXT REQUEST FIX: PASSED")
        print("The fix correctly uses the project directory from command line arguments.")
    else:
        print("\n❌ REAL CONTEXT REQUEST FIX: FAILED")
        print("The fix needs further investigation.")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

## ANALYSIS INSTRUCTIONS
Based on the 8 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: Why is my context selection taking so long?

Provide specific, actionable insights based on this focused context.
