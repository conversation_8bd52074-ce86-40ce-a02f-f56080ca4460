# USER QUERY
Why is my context selection taking so long?

# INTELLIGENT CONTEXT ANALYSIS
## Task: debugging
## Focus: Debug performance issues in context selection with inheritance analysis

## CRITICAL ENTITIES (8 most important)

### 1. run_enhanced_ir_pipeline (function)
- File: mid_level_ir_with_inheritance.py


- Criticality: high | Risk: high
- **Calls**: ["time", "create_enhanced_config", "IRContext", "Path", "FileScanner", "..."] (total: 20)
- **Used by**: ["test_enhanced_llm_package_demo", "test_fixed_icd_with_inheritance", "test_inheritance_integration", "test_complete_inheritance_package", "test_simple_icd_with_inheritance", "..."] (total: 8)
- **Side Effects**: network_io, writes_log

### 2. parse_context_request (function)
- File: context_request_handler.py


- Criticality: high | Risk: medium
- **Calls**: ["search", "strip", "group", "rstrip", "startswith", "..."] (total: 13)
- **Used by**: ["test_directory_file_format", "test_context_request_format_fix", "test_aider_context_request_integration", "test_json_parsing_fix", "aider_context_request_integration", "..."] (total: 6)
- **Side Effects**: network_io, writes_log

### 3. process_context_requests (method)
- File: aider-main\aider\coders\base_coder.py
- Belongs to Class: `BaseCoder`
- Inherits From: No inheritance detected
- Criticality: high | Risk: high

#### 🔁 Class Context
- Part of `BaseCoder` class

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ["tool_warning", "getcwd", "basename", "exists", "join", "..."] (total: 20)
- **Used by**: ["test_repo_map_compatibility", "test_aider_context_request", "test_llm_workflow_understanding", "test_context_request_hang", "test_aider_coder_path_fix", "..."] (total: 9)
- **Side Effects**: network_io, database_io, modifies_state

### 4. process_context_request (method)
- File: context_request_handler.py
- Belongs to Class: `ContextRequestHandler`
- Inherits From: No inheritance detected
- Criticality: high | Risk: high

#### 🔁 Class Context
- Part of `ContextRequestHandler` class

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ["join", "_get_from_cache", "_extract_symbol_content", "_extract_essential_imports", "SymbolInfo", "..."] (total: 9)
- **Used by**: ["base_coder_old", "test_surgical_integration", "context_request_demo", "test_context_request_integration", "test_surgical_extraction_integration", "..."] (total: 10)
- **Side Effects**: modifies_state, database_io, network_io

### 5. process_context_requests (method)
- File: aider-main\aider\coders\base_coder_old.py
- Belongs to Class: `BaseCoderOld`
- Inherits From: No inheritance detected
- Criticality: high | Risk: high

#### 🔁 Class Context
- Part of `BaseCoderOld` class

#### 🧩 Method Details
- Calls super(): No
- **Calls**: ["tool_warning", "AiderContextRequestIntegration", "tool_error", "detect_context_request", "tool_output", "..."] (total: 13)
- **Used by**: ["test_repo_map_compatibility", "test_aider_context_request", "test_llm_workflow_understanding", "test_context_request_hang", "test_aider_coder_path_fix", "..."] (total: 9)
- **Side Effects**: network_io, modifies_state, writes_log

### 6. test_all_possible_context_leaks (function)
- File: test_hidden_context_detection.py


- Criticality: high | Risk: medium
- **Calls**: ["Model", "InputOutput", "GitRepo", "create", "fmt_system_prompt", "..."] (total: 8)
- **Used by**: []
- **Side Effects**: network_io, writes_log

### 7. main (function)
- File: mid_level_ir_with_inheritance.py


- Criticality: high | Risk: medium
- **Calls**: ["run_enhanced_ir_pipeline", "open", "dump", "stat", "Path", "..."] (total: 6)
- **Used by**: ["test_main", "test_ssl_verification", "test_deprecated", "test_browser"] (total: 4)
- **Side Effects**: modifies_file, writes_log

### 8. process_context_request (function)
- File: aider_context_request_integration.py


- Criticality: medium | Risk: medium
- **Calls**: ["process_context_request", "get", "render_augmented_prompt"] (total: 3)
- **Used by**: ["base_coder_old", "test_surgical_integration", "context_request_demo", "test_context_request_integration", "test_surgical_extraction_integration", "..."] (total: 10)
- **Side Effects**: modifies_state, network_io, writes_log

## KEY IMPLEMENTATIONS (8 functions)

### 1. run_enhanced_ir_pipeline
```python
def run_enhanced_ir_pipeline(project_path: str) -> Dict[str, Any]:
    """
    Run the enhanced IR generation pipeline with inheritance analysis.
    
    Args:
        project_path: Path to the project to analyze
        
    Returns:
        Complete IR data with inheritance information
    """
    start_time = time.time()
    config = create_enhanced_config()
    
    print("🔍 Enhanced Mid-Level IR Generation with Inheritance Analysis")
    print("=" * 70)
    print(f"Project: {project_path}")
    print()
    
    # Initialize context
    context = IRContext(project_path=Path(project_path))
    
    # Step 1: File Discovery
    print("📁 Step 1: File Discovery")
    file_scanner = FileScanner(config)
    context = file_scanner.scan(context)
    print(f"   Found {len(context.all_files)} Python files")
    print()
    # ... (implementation continues)
```

### 2. parse_context_request
```python
    def parse_context_request(self, request_text: str) -> Optional[ContextRequest]:
        """
        Parse a context request from the LLM response.

        Args:
            request_text: The text containing the context request

        Returns:
            A ContextRequest object or None if the request is invalid
        """
        try:
            # Extract the JSON object from the request text
            pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}\}'
            match = re.search(pattern, request_text, re.DOTALL)
            if not match:
                # Try alternative pattern
                pattern = r'\{CONTEXT_REQUEST:\s*(.*)'
                match = re.search(pattern, request_text, re.DOTALL)
                if not match:
                    return None

            # Get the matched content
    # ... (implementation continues)
```

### 3. process_context_requests
```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
    # ... (implementation continues)
```

### 4. process_context_request
```python
    def process_context_request(self, request: ContextRequest) -> Dict[str, Any]:
        """
        Process a context request, extracting the requested symbols and their dependencies.

        Args:
            request: The context request to process

        Returns:
            A dictionary containing the extracted context
        """
        # Create a cache key for this request
        cache_key = f"context_request:{','.join([s.name for s in request.symbols_of_interest])}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        result = {
            "original_user_query_context": request.original_user_query_context,
            "reason_for_request": request.reason_for_request,
            "extracted_symbols": [],
    # ... (implementation continues)
```

### 5. process_context_request
```python
    def process_context_request(self,
                               context_request: ContextRequest,
                               original_user_query: str,
                               repo_overview: str) -> str:
        """
        Process a context request and generate an augmented prompt.

        Args:
            context_request: The context request to process
            original_user_query: The original user query
            repo_overview: The repository overview

        Returns:
            An augmented prompt with the extracted context
        """
        # Log the inputs
        print("\n\n=== CONTEXT REQUEST PROCESSING ===")
        print(f"Original user query: {original_user_query}")
        print(f"Context request: {context_request}")
    # ... (implementation continues)
```

### 6. process_context_requests
```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
    # ... (implementation continues)
```

### 7. test_all_possible_context_leaks
```python
def test_all_possible_context_leaks():
    """Test ALL possible ways repository context could leak to the LLM."""
    print("🔍 Hidden Repository Context Detection")
    print("=" * 80)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        print("✅ Smart Map Request System is available")
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
    # ... (implementation continues)
```

### 8. main
```python
def main():
    """Main function to run enhanced IR generation."""
    if len(sys.argv) > 1:
        project_path = sys.argv[1]
    else:
        project_path = "."
    
    try:
        # Generate enhanced IR with inheritance analysis
        ir_data = run_enhanced_ir_pipeline(project_path)
        
        # Save to file
        output_file = "enhanced_ir_with_inheritance.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(ir_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Enhanced IR with inheritance data saved to: {output_file}")
        
        # Calculate file size
        file_size = Path(output_file).stat().st_size
        print(f"📁 Output size: {file_size / 1024 / 1024:.1f} MB")
        
        return True
        
    except Exception as e:
    # ... (implementation continues)
```

## ANALYSIS INSTRUCTIONS
Based on the 8 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: Why is my context selection taking so long?

Provide specific, actionable insights based on this focused context.
